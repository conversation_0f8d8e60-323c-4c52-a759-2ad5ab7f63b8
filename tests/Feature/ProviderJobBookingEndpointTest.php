<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\JobBooking;
use App\Models\Bid;
use App\Enums\RoleEnum;
use App\Enums\JobBookingStatusEnum;
use App\Enums\BidStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;
use Laravel\Passport\Passport;

class ProviderJobBookingEndpointTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => RoleEnum::CONSUMER, 'guard_name' => 'api']);
        Role::create(['name' => RoleEnum::PROVIDER, 'guard_name' => 'api']);
        Role::create(['name' => RoleEnum::ADMIN, 'guard_name' => 'api']);
    }

    /** @test */
    public function provider_can_get_job_booking_with_bids()
    {
        // Create a provider user
        $provider = User::factory()->create();
        $provider->assignRole(RoleEnum::PROVIDER);

        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a job booking
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::OPEN
        ]);

        // Create some bids for the job booking
        $bid1 = Bid::factory()->create([
            'job_booking_id' => $jobBooking->id,
            'provider_id' => $provider->id,
            'amount' => 100.00,
            'status' => BidStatusEnum::REQUESTED->value
        ]);

        $otherProvider = User::factory()->create();
        $otherProvider->assignRole(RoleEnum::PROVIDER);

        $bid2 = Bid::factory()->create([
            'job_booking_id' => $jobBooking->id,
            'provider_id' => $otherProvider->id,
            'amount' => 150.00,
            'status' => BidStatusEnum::REQUESTED->value
        ]);

        // Authenticate as provider
        Passport::actingAs($provider, [], 'api');

        // Make request to the new endpoint
        $response = $this->getJson("/api/provider/job-bookings/{$jobBooking->job_uuid}");

        // Assert response
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'jobId',
                        'status',
                        'bids_summary' => [
                            'total_bids',
                            'pending_bids',
                            'accepted_bids',
                            'rejected_bids'
                        ],
                        'bids' => [
                            '*' => [
                                'id',
                                'amount',
                                'status',
                                'provider_id'
                            ]
                        ]
                    ],
                    'message'
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'jobId' => $jobBooking->job_uuid,
                        'bids_summary' => [
                            'total_bids' => 2,
                            'pending_bids' => 2
                        ]
                    ]
                ]);
    }

    /** @test */
    public function non_provider_cannot_access_provider_job_booking_endpoint()
    {
        // Create a consumer user
        $consumer = User::factory()->create();
        $consumer->assignRole(RoleEnum::CONSUMER);

        // Create a job booking
        $jobBooking = JobBooking::factory()->create([
            'user_id' => $consumer->id,
            'status' => JobBookingStatusEnum::OPEN
        ]);

        // Authenticate as consumer (not provider)
        Passport::actingAs($consumer, [], 'api');

        // Make request to the provider endpoint
        $response = $this->getJson("/api/provider/job-bookings/{$jobBooking->job_uuid}");

        // Should be forbidden
        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHORIZED',
                        'message' => 'Only providers can access this endpoint.'
                    ]
                ]);
    }

    /** @test */
    public function provider_gets_404_for_non_existent_job_booking()
    {
        // Create a provider user
        $provider = User::factory()->create();
        $provider->assignRole(RoleEnum::PROVIDER);

        // Authenticate as provider
        Passport::actingAs($provider, [], 'api');

        // Make request with non-existent job UUID
        $response = $this->getJson("/api/provider/job-bookings/non-existent-uuid");

        // Should return 404
        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'code' => 'JOB_BOOKING_NOT_FOUND',
                        'message' => 'Job booking not found.'
                    ]
                ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_provider_endpoint()
    {
        // Create a job booking
        $jobBooking = JobBooking::factory()->create([
            'status' => JobBookingStatusEnum::OPEN
        ]);

        // Make request without authentication
        $response = $this->getJson("/api/provider/job-bookings/{$jobBooking->job_uuid}");

        // Should be unauthorized
        $response->assertStatus(401);
    }
}
