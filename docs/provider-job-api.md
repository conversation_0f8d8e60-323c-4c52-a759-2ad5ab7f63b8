# Provider Job Management API

This document describes the API endpoints for providers to manage their actual jobs (jobs created from accepted bids) on the provider dashboard.

## Overview

When a customer accepts a provider's bid, the system automatically creates a **Job** record in the `business_jobs` table. This job represents the actual work assignment that the provider needs to complete.

The workflow is:
1. Customer creates a **Job Booking**
2. Providers submit **Bids** on the job booking
3. Customer **accepts a bid**
4. System creates an actual **Job** record
5. Provider can now manage this job through the dashboard

## Authentication

All endpoints require authentication with a provider role:
- Header: `Authorization: Bearer {api_token}`
- User must have the `PROVIDER` role

## Endpoints

### 1. Get Provider Jobs

**GET** `/api/provider/jobs`

Retrieve a paginated list of jobs assigned to the authenticated provider.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `per_page` | integer | Number of jobs per page (default: 15) |
| `status` | string | Filter by job status: `assigned`, `in_progress`, `completed`, `cancelled` |
| `job_booking_id` | integer | Filter by specific job booking ID |
| `date_from` | date | Filter jobs created from this date (Y-m-d format) |
| `date_to` | date | Filter jobs created until this date (Y-m-d format) |
| `completion_date_from` | date | Filter by estimated completion date from |
| `completion_date_to` | date | Filter by estimated completion date to |
| `order_by` | string | Order by field: `created_at`, `estimated_completion_time`, `agreed_amount`, `status` |
| `order_direction` | string | Order direction: `asc`, `desc` (default: `desc`) |

#### Example Request

```bash
GET /api/provider/jobs?status=assigned&per_page=10&order_by=estimated_completion_time&order_direction=asc
```

#### Example Response

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "job_uuid": "550e8400-e29b-41d4-a716-************",
      "status": "assigned",
      "agreed_amount": "150.00",
      "estimated_completion_time": "2024-01-15 14:00:00",
      "actual_start_time": null,
      "actual_completion_time": null,
      "notes": null,
      "created_at": "2024-01-10 10:00:00",
      "updated_at": "2024-01-10 10:00:00",
      "job_booking": {
        "id": 1,
        "job_uuid": "550e8400-e29b-41d4-a716-************",
        "project_code": "JOB-2024-001",
        "job_type": "one_time",
        "service_category": "cleaning",
        "schedule_date": "2024-01-15",
        "address": "123 Main St",
        "city": "New York",
        "description": "Deep cleaning of 3-bedroom apartment",
        "contact_name": "John Doe",
        "contact_phone": "+**********",
        "contact_email": "<EMAIL>"
      },
      "customer": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+**********"
      },
      "bid": {
        "id": 1,
        "amount": "150.00",
        "description": "Professional cleaning service",
        "status": "accepted"
      },
      "status_display": "Assigned",
      "can_start": true,
      "can_complete": false,
      "can_cancel": true,
      "is_assigned": true,
      "is_in_progress": false,
      "is_completed": false,
      "is_cancelled": false,
      "duration_minutes": null,
      "is_overdue": false,
      "time_until_estimated_completion": "in 5 days"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total": 25,
    "last_page": 3,
    "from": 1,
    "to": 10
  }
}
```

### 2. Get Single Job

**GET** `/api/provider/jobs/{job_id}`

Retrieve details of a specific job.

#### Example Response

Same structure as individual job in the list above, with all relationships loaded.

### 3. Update Job Status

**PATCH** `/api/provider/jobs/{job_id}/status`

Update the status of a job with optional notes.

#### Request Body

```json
{
  "status": "in_progress",
  "notes": "Started cleaning the kitchen area"
}
```

#### Valid Status Transitions

- `assigned` → `in_progress` or `cancelled`
- `in_progress` → `completed` or `cancelled`
- `completed` → (no transitions allowed)
- `cancelled` → (no transitions allowed)

### 4. Start Job

**POST** `/api/provider/jobs/{job_id}/start`

Start a job (transition from `assigned` to `in_progress`). Automatically sets `actual_start_time`.

#### Example Response

```json
{
  "success": true,
  "data": { /* job object */ },
  "message": "Job started successfully."
}
```

### 5. Complete Job

**POST** `/api/provider/jobs/{job_id}/complete`

Complete a job (transition from `in_progress` to `completed`). Automatically sets `actual_completion_time`.

#### Request Body (Optional)

```json
{
  "notes": "Job completed successfully. All areas cleaned as requested."
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  }
}
```

### Common Error Codes

- `UNAUTHORIZED` - User is not authenticated or not a provider
- `FORBIDDEN` - Provider trying to access jobs they don't own
- `FETCH_JOBS_FAILED` - General error fetching jobs
- `INVALID_STATUS_TRANSITION` - Invalid status change attempted
- `CANNOT_START_JOB` - Job cannot be started (not in assigned status)
- `CANNOT_COMPLETE_JOB` - Job cannot be completed (not in progress)

## Frontend Integration

### Dashboard Job List

Use the `/api/provider/jobs` endpoint to display jobs on the provider dashboard:

```javascript
// Fetch assigned jobs
const response = await fetch('/api/provider/jobs?status=assigned&order_by=estimated_completion_time', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Accept': 'application/json'
  }
});

const data = await response.json();
if (data.success) {
  // Display jobs in dashboard
  data.data.forEach(job => {
    displayJob(job);
  });
}
```

### Job Status Management

```javascript
// Start a job
const startJob = async (jobId) => {
  const response = await fetch(`/api/provider/jobs/${jobId}/start`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    }
  });
  
  const data = await response.json();
  if (data.success) {
    // Update UI to show job is in progress
    updateJobStatus(jobId, 'in_progress');
  }
};

// Complete a job with notes
const completeJob = async (jobId, notes) => {
  const response = await fetch(`/api/provider/jobs/${jobId}/complete`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify({ notes })
  });
  
  const data = await response.json();
  if (data.success) {
    // Update UI to show job is completed
    updateJobStatus(jobId, 'completed');
  }
};
```

### Filtering and Sorting

```javascript
// Get jobs with filters
const getFilteredJobs = async (filters) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/provider/jobs?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    }
  });
  
  return response.json();
};

// Example usage
const todayJobs = await getFilteredJobs({
  status: 'assigned',
  completion_date_from: '2024-01-15',
  completion_date_to: '2024-01-15',
  order_by: 'estimated_completion_time'
});
```

## Status Helpers

The API response includes helpful status flags:

- `can_start` - Whether the job can be started
- `can_complete` - Whether the job can be completed  
- `can_cancel` - Whether the job can be cancelled
- `is_overdue` - Whether the job is past its estimated completion time
- `time_until_estimated_completion` - Human-readable time until completion

Use these to show/hide action buttons and display status indicators in the UI.
