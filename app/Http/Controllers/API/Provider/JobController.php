<?php

namespace App\Http\Controllers\API\Provider;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Http\Resources\JobResource;
use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class JobController extends Controller
{
    public function __construct()
    {
        // Ensure only providers can access these endpoints
        $this->middleware(function ($request, $next) {
            $user = Auth::guard('api')->user();
            if (!$user || !$user->hasRole(RoleEnum::PROVIDER)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHORIZED',
                        'message' => 'Only providers can access this endpoint.',
                    ]
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the provider's jobs.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Get provider ID directly from auth
            $user = Auth::guard('api')->user();
            $providerId = $user->id;
            $perPage = $request->input('per_page', 15);

            // Query jobs for this provider
            $query = Job::where('provider_id', $providerId)
                ->with(['jobBooking', 'customer', 'provider', 'bid', 'booking']);

            // Apply optional filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('job_booking_id')) {
                $query->where('job_booking_id', $request->input('job_booking_id'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            // Filter by estimated completion time
            if ($request->filled('completion_date_from')) {
                $query->whereDate('estimated_completion_time', '>=', $request->input('completion_date_from'));
            }

            if ($request->filled('completion_date_to')) {
                $query->whereDate('estimated_completion_time', '<=', $request->input('completion_date_to'));
            }

            // Order by creation date (newest first) or by estimated completion time
            $orderBy = $request->input('order_by', 'created_at');
            $orderDirection = $request->input('order_direction', 'desc');
            
            if (in_array($orderBy, ['created_at', 'estimated_completion_time', 'agreed_amount', 'status'])) {
                $query->orderBy($orderBy, $orderDirection);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $jobs = $query->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => JobResource::collection($jobs),
                'pagination' => [
                    'current_page' => $jobs->currentPage(),
                    'per_page' => $jobs->perPage(),
                    'total' => $jobs->total(),
                    'last_page' => $jobs->lastPage(),
                    'from' => $jobs->firstItem(),
                    'to' => $jobs->lastItem(),
                ],
                'filters' => [
                    'status' => $request->input('status'),
                    'job_booking_id' => $request->input('job_booking_id'),
                    'date_from' => $request->input('date_from'),
                    'date_to' => $request->input('date_to'),
                    'completion_date_from' => $request->input('completion_date_from'),
                    'completion_date_to' => $request->input('completion_date_to'),
                    'order_by' => $orderBy,
                    'order_direction' => $orderDirection,
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch provider jobs: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_JOBS_FAILED',
                    'message' => 'Failed to fetch jobs.',
                ]
            ], 500);
        }
    }

    /**
     * Display the specified job.
     *
     * @param Job $job
     * @return JsonResponse
     */
    public function show(Job $job): JsonResponse
    {
        try {
            // Check if the provider owns this job
            if ($job->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only view your own jobs.',
                    ]
                ], 403);
            }

            $job->load(['jobBooking', 'customer', 'provider', 'bid', 'booking']);

            return response()->json([
                'success' => true,
                'data' => new JobResource($job)
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch job: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_JOB_FAILED',
                    'message' => 'Failed to fetch job details.',
                ]
            ], 500);
        }
    }

    /**
     * Update job status.
     *
     * @param Request $request
     * @param Job $job
     * @return JsonResponse
     */
    public function updateStatus(Request $request, Job $job): JsonResponse
    {
        try {
            // Check if the provider owns this job
            if ($job->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only update your own jobs.',
                    ]
                ], 403);
            }

            $request->validate([
                'status' => 'required|in:assigned,in_progress,completed,cancelled',
                'notes' => 'nullable|string|max:1000'
            ]);

            $newStatus = $request->input('status');
            $notes = $request->input('notes');

            // Validate status transition
            if (!$this->canTransitionTo($job, $newStatus)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_STATUS_TRANSITION',
                        'message' => "Cannot transition from '{$job->status}' to '{$newStatus}'.",
                    ]
                ], 422);
            }

            DB::beginTransaction();

            $oldStatus = $job->status;
            $job->status = $newStatus;
            
            // Update timestamps based on status
            if ($newStatus === 'in_progress' && !$job->actual_start_time) {
                $job->actual_start_time = now();
            } elseif ($newStatus === 'completed' && !$job->actual_completion_time) {
                $job->actual_completion_time = now();
            }

            // Update notes if provided
            if ($notes) {
                $job->notes = $notes;
            }

            $job->save();

            DB::commit();

            $job->load(['jobBooking', 'customer', 'provider', 'bid', 'booking']);

            return response()->json([
                'success' => true,
                'data' => new JobResource($job),
                'message' => "Job status updated from '{$oldStatus}' to '{$newStatus}' successfully."
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to update job status: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UPDATE_STATUS_FAILED',
                    'message' => 'Failed to update job status.',
                ]
            ], 500);
        }
    }

    /**
     * Start a job.
     *
     * @param Request $request
     * @param Job $job
     * @return JsonResponse
     */
    public function start(Request $request, Job $job): JsonResponse
    {
        try {
            // Check if the provider owns this job
            if ($job->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only start your own jobs.',
                    ]
                ], 403);
            }

            if (!$job->start()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_START_JOB',
                        'message' => 'Job cannot be started. It must be in assigned status.',
                    ]
                ], 422);
            }

            $job->load(['jobBooking', 'customer', 'provider', 'bid', 'booking']);

            return response()->json([
                'success' => true,
                'data' => new JobResource($job),
                'message' => 'Job started successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to start job: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'START_JOB_FAILED',
                    'message' => 'Failed to start job.',
                ]
            ], 500);
        }
    }

    /**
     * Complete a job.
     *
     * @param Request $request
     * @param Job $job
     * @return JsonResponse
     */
    public function complete(Request $request, Job $job): JsonResponse
    {
        try {
            // Check if the provider owns this job
            if ($job->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only complete your own jobs.',
                    ]
                ], 403);
            }

            $request->validate([
                'notes' => 'nullable|string|max:1000'
            ]);

            if (!$job->complete()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_COMPLETE_JOB',
                        'message' => 'Job cannot be completed. It must be in progress.',
                    ]
                ], 422);
            }

            // Update notes if provided
            if ($request->filled('notes')) {
                $job->notes = $request->input('notes');
                $job->save();
            }

            $job->load(['jobBooking', 'customer', 'provider', 'bid', 'booking']);

            return response()->json([
                'success' => true,
                'data' => new JobResource($job),
                'message' => 'Job completed successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to complete job: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'COMPLETE_JOB_FAILED',
                    'message' => 'Failed to complete job.',
                ]
            ], 500);
        }
    }

    /**
     * Check if job can transition to the given status.
     *
     * @param Job $job
     * @param string $newStatus
     * @return bool
     */
    private function canTransitionTo(Job $job, string $newStatus): bool
    {
        $currentStatus = $job->status;

        $allowedTransitions = [
            'assigned' => ['in_progress', 'cancelled'],
            'in_progress' => ['completed', 'cancelled'],
            'completed' => [], // No transitions from completed
            'cancelled' => [], // No transitions from cancelled
        ];

        return in_array($newStatus, $allowedTransitions[$currentStatus] ?? []);
    }
}
