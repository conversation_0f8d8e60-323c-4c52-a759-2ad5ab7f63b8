<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_uuid' => $this->job_uuid,
            'job_booking_id' => $this->job_booking_id,
            'bid_id' => $this->bid_id,
            'booking_id' => $this->booking_id,
            'customer_id' => $this->customer_id,
            'provider_id' => $this->provider_id,
            'status' => $this->status,
            'agreed_amount' => $this->agreed_amount,
            'estimated_completion_time' => $this->estimated_completion_time?->format('Y-m-d H:i:s'),
            'actual_start_time' => $this->actual_start_time?->format('Y-m-d H:i:s'),
            'actual_completion_time' => $this->actual_completion_time?->format('Y-m-d H:i:s'),
            'notes' => $this->notes,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Relationships
            'job_booking' => $this->whenLoaded('jobBooking', function () {
                return [
                    'id' => $this->jobBooking->id,
                    'job_uuid' => $this->jobBooking->job_uuid,
                    'project_code' => $this->jobBooking->project_code,
                    'job_type' => $this->jobBooking->job_type,
                    'property_type' => $this->jobBooking->property_type,
                    'service_category' => $this->jobBooking->service_category,
                    'schedule_date' => $this->jobBooking->schedule_date?->format('Y-m-d'),
                    'time_preference' => $this->jobBooking->time_preference,
                    'frequency' => $this->jobBooking->frequency,
                    'recurring_frequency' => $this->jobBooking->recurring_frequency,
                    'address' => $this->jobBooking->address,
                    'city' => $this->jobBooking->city,
                    'state' => $this->jobBooking->state,
                    'postal_code' => $this->jobBooking->postal_code,
                    'country' => $this->jobBooking->country,
                    'latitude' => $this->jobBooking->latitude,
                    'longitude' => $this->jobBooking->longitude,
                    'description' => $this->jobBooking->description,
                    'special_instructions' => $this->jobBooking->special_instructions,
                    'contact_name' => $this->jobBooking->contact_name,
                    'contact_phone' => $this->jobBooking->contact_phone,
                    'contact_email' => $this->jobBooking->contact_email,
                    'status' => $this->jobBooking->status,
                    'created_at' => $this->jobBooking->created_at?->format('Y-m-d H:i:s'),
                ];
            }),
            
            'provider' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'name' => $this->provider->name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),
            
            'customer' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->id,
                    'name' => $this->customer->name,
                    'email' => $this->customer->email,
                    'phone' => $this->customer->phone,
                ];
            }),
            
            'bid' => $this->whenLoaded('bid', function () {
                return [
                    'id' => $this->bid->id,
                    'amount' => $this->bid->amount,
                    'description' => $this->bid->description,
                    'estimated_completion_time' => $this->bid->estimated_completion_time?->format('Y-m-d H:i:s'),
                    'status' => $this->bid->status,
                ];
            }),
            
            'booking' => $this->whenLoaded('booking', function () {
                return [
                    'id' => $this->booking->id,
                    'status' => $this->booking->status,
                    'scheduled_date' => $this->booking->scheduled_date?->format('Y-m-d'),
                    'start_time' => $this->booking->start_time,
                    'end_time' => $this->booking->end_time,
                ];
            }),

            // Status helpers
            'status_display' => $this->getStatusDisplay(),
            'can_start' => $this->canStart(),
            'can_complete' => $this->canComplete(),
            'can_cancel' => $this->canCancel(),
            'is_assigned' => $this->isAssigned(),
            'is_in_progress' => $this->isInProgress(),
            'is_completed' => $this->isCompleted(),
            'is_cancelled' => $this->isCancelled(),

            // Time calculations
            'duration_minutes' => $this->getDurationInMinutes(),
            'is_overdue' => $this->isOverdue(),
            'time_until_estimated_completion' => $this->getTimeUntilEstimatedCompletion(),
        ];
    }

    /**
     * Get human-readable status display
     */
    private function getStatusDisplay(): string
    {
        return match($this->status) {
            'assigned' => 'Assigned',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    /**
     * Check if job can be started
     */
    private function canStart(): bool
    {
        return $this->status === 'assigned';
    }

    /**
     * Check if job can be completed
     */
    private function canComplete(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if job can be cancelled
     */
    private function canCancel(): bool
    {
        return in_array($this->status, ['assigned', 'in_progress']);
    }

    /**
     * Check if job is assigned
     */
    private function isAssigned(): bool
    {
        return $this->status === 'assigned';
    }

    /**
     * Check if job is in progress
     */
    private function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if job is completed
     */
    private function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if job is cancelled
     */
    private function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get job duration in minutes
     */
    private function getDurationInMinutes(): ?int
    {
        if (!$this->actual_start_time || !$this->actual_completion_time) {
            return null;
        }

        return $this->actual_start_time->diffInMinutes($this->actual_completion_time);
    }

    /**
     * Check if job is overdue
     */
    private function isOverdue(): bool
    {
        if (!$this->estimated_completion_time || $this->isCompleted() || $this->isCancelled()) {
            return false;
        }

        return now()->isAfter($this->estimated_completion_time);
    }

    /**
     * Get time until estimated completion
     */
    private function getTimeUntilEstimatedCompletion(): ?string
    {
        if (!$this->estimated_completion_time || $this->isCompleted() || $this->isCancelled()) {
            return null;
        }

        $now = now();
        if ($now->isAfter($this->estimated_completion_time)) {
            return 'Overdue by ' . $now->diffForHumans($this->estimated_completion_time, true);
        }

        return $this->estimated_completion_time->diffForHumans($now);
    }
}
