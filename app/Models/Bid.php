<?php

namespace App\Models;

use App\Models\User;
use App\Models\JobBooking;
use App\Enums\BidStatusEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bid extends Model
{
    use HasFactory;

    protected $table = 'bids';

    protected $fillable = [
        'id',
        'job_booking_id',
        'provider_id',
        'amount',
        'description',
        'status',
        'estimated_completion_time'
    ];

    // Removed automatic eager loading to prevent issues with orphaned bids
    // Relationships should be loaded explicitly in controllers when needed
    protected $with = [];

    protected $casts = [
        'job_booking_id' => 'integer',
        'provider_id' => 'integer',
        'amount' => 'float',
        'estimated_completion_time' => 'datetime',
    ];

    /**
     * Get the job booking that owns the bid.
     */
    public function jobBooking(): BelongsTo
    {
        return $this->belongsTo(JobBooking::class, 'job_booking_id');
    }

    /**
     * Get the provider that owns the bid.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the job created from this bid (if accepted).
     */
    public function job(): HasOne
    {
        return $this->hasOne(Job::class, 'bid_id');
    }

    // ==================== STATUS HELPER METHODS ====================

    /**
     * Check if bid is requested (pending)
     */
    public function isRequested(): bool
    {
        return $this->status === BidStatusEnum::REQUESTED->value;
    }

    /**
     * Check if bid is accepted
     */
    public function isAccepted(): bool
    {
        return $this->status === BidStatusEnum::ACCEPTED->value;
    }

    /**
     * Check if bid is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === BidStatusEnum::REJECTED->value;
    }

    /**
     * Check if bid is withdrawn
     */
    public function isWithdrawn(): bool
    {
        return $this->status === BidStatusEnum::WITHDRAWN->value;
    }

    /**
     * Check if bid is in final status (rejected or withdrawn)
     */
    public function isFinal(): bool
    {
        return BidStatusEnum::isFinal($this->status);
    }

    /**
     * Check if bid is active (can still be modified)
     */
    public function isActive(): bool
    {
        return BidStatusEnum::isActive($this->status);
    }

    // ==================== WORKFLOW TRANSITION METHODS ====================

    /**
     * Accept this bid
     */
    public function accept(): bool
    {
        if (!$this->isRequested()) {
            return false;
        }

        $this->status = BidStatusEnum::ACCEPTED->value;
        return $this->save();
    }

    /**
     * Reject this bid
     */
    public function reject(): bool
    {
        if (!$this->isRequested()) {
            return false;
        }

        $this->status = BidStatusEnum::REJECTED->value;
        return $this->save();
    }

    /**
     * Withdraw this bid
     */
    public function withdraw(): bool
    {
        if (!$this->isRequested()) {
            return false;
        }

        $this->status = BidStatusEnum::WITHDRAWN->value;
        return $this->save();
    }

    /**
     * Check if bid can be withdrawn
     */
    public function canWithdraw(): bool
    {
        return $this->isRequested();
    }

    /**
     * Check if bid can be accepted
     */
    public function canAccept(): bool
    {
        return $this->isRequested() && $this->jobBooking->allowsBidding();
    }

    /**
     * Check if bid can be rejected
     */
    public function canReject(): bool
    {
        return $this->isRequested();
    }

    /**
     * Get bid status display name
     */
    public function getStatusDisplayName(): string
    {
        return match ($this->status) {
            BidStatusEnum::REQUESTED->value => 'Pending Review',
            BidStatusEnum::ACCEPTED->value => 'Accepted',
            BidStatusEnum::REJECTED->value => 'Rejected',
            BidStatusEnum::WITHDRAWN->value => 'Withdrawn',
            default => 'Unknown Status',
        };
    }

    /**
     * Get available actions for current bid status
     */
    public function getAvailableActions(): array
    {
        $actions = [];

        if ($this->canWithdraw()) {
            $actions[] = 'withdraw';
        }

        if ($this->canAccept()) {
            $actions[] = 'accept';
        }

        if ($this->canReject()) {
            $actions[] = 'reject';
        }

        return $actions;
    }

    /**
     * Check if this bid is the winning bid
     */
    public function isWinningBid(): bool
    {
        return $this->isAccepted() && $this->jobBooking->isAssigned();
    }

    /**
     * Get bid ranking among all bids for the same job booking
     */
    public function getRanking(): int
    {
        return $this->jobBooking->bids()
            ->where('amount', '<=', $this->amount)
            ->count();
    }

    /**
     * Check if this is the lowest bid
     */
    public function isLowestBid(): bool
    {
        $lowestAmount = $this->jobBooking->bids()->min('amount');
        return $this->amount == $lowestAmount;
    }

    // ==================== QUERY SCOPES ====================

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get requested bids
     */
    public function scopeRequested($query)
    {
        return $query->where('status', BidStatusEnum::REQUESTED->value);
    }

    /**
     * Scope to get accepted bids
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', BidStatusEnum::ACCEPTED->value);
    }

    /**
     * Scope to get rejected bids
     */
    public function scopeRejected($query)
    {
        return $query->where('status', BidStatusEnum::REJECTED->value);
    }

    /**
     * Scope to get withdrawn bids
     */
    public function scopeWithdrawn($query)
    {
        return $query->where('status', BidStatusEnum::WITHDRAWN->value);
    }

    /**
     * Scope to get active bids (requested or accepted)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', BidStatusEnum::getActiveStatuses());
    }

    /**
     * Scope to get final bids (rejected or withdrawn)
     */
    public function scopeFinal($query)
    {
        return $query->whereIn('status', BidStatusEnum::getFinalStatuses());
    }

    /**
     * Scope to get bids by provider
     */
    public function scopeByProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope to get bids by job booking
     */
    public function scopeByJobBooking($query, $jobBookingId)
    {
        return $query->where('job_booking_id', $jobBookingId);
    }

    /**
     * Scope to order by amount (lowest first)
     */
    public function scopeOrderByAmount($query, $direction = 'asc')
    {
        return $query->orderBy('amount', $direction);
    }

    /**
     * Scope to order by estimated completion time
     */
    public function scopeOrderByCompletionTime($query, $direction = 'asc')
    {
        return $query->orderBy('estimated_completion_time', $direction);
    }
}
